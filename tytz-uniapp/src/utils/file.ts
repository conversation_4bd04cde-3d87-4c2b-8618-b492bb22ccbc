// import type { UploadMethod, UploadFile } from '@/uni_modules/wot-design-uni/components/wd-upload/types'
import { defaultConfigs } from '@/const/configs'
import { getEnvBaseUrl } from '@/utils/index'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'

/**
 * 获取文件名和文件名
 */
export function getFileName(path: string) {
  const fileName = path.split('/').pop()
  const baseName = fileName.split('.').shift()
  return { fileName, baseName }
}

/** 获取文件后缀 */
export function getFileType(path: string) {
  const fileName = path.split('/').pop()
  const type = fileName.split('.').pop()
  return type
}

/**
 * 补全文件资源路径
 */
export function completionOriginUrl(url: string) {
  let retUrl = url
  const { VITE_APP_PROXY_PREFIX, VITE_APP_PROXY_TARGET } = import.meta.env
  retUrl = VITE_APP_PROXY_TARGET + VITE_APP_PROXY_PREFIX + url

  // #ifdef MP-WEIXIN
  retUrl = getEnvBaseUrl() + url
  // #endif

  return retUrl
}

/** 自定义上传文件 */

// const fileList = ref<UploadFile[]>([])
// const customUpload: UploadMethod = (file, formData, options) => {
export const customUpload = (file, formData, options) => {
  const accessToken = uni.getStorageSync(ACCESS_TOKEN)
  const Authorization = accessToken ? `${uni.getStorageSync(TOKEN_TYPE)} ${accessToken}` : ''

  const uploadTask = uni.uploadFile({
    url: defaultConfigs.uploadUrl,
    header: {
      Authorization,
      ...options.header,
    },
    name: options.name,
    fileName: options.name,
    fileType: options.fileType,
    formData: {
      ...formData,
      filename: file.name,
    },
    file,
    filePath: file.url,
    success(res) {
      try {
        if (res.statusCode === options.statusCode) {
          options.onSuccess(res, file, formData)
        } else {
          // 设置上传失败
          options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)
        }
      } catch (error) {
        options.onError(error, file, formData)
      }
    },
    fail(err) {
      // 设置上传失败
      options.onError(err, file, formData)
    },
  })
  // 设置当前文件加载的百分比
  uploadTask.onProgressUpdate((res) => {
    options.onProgress(res, file)
  })
}
