import PLATFORM from '@/utils/platform'
import { getFileType } from '@/utils/file'

/** 通过传入文件名后缀type的类型判断是否是图片 */
export const isImage = (type: string) => {
  const imgTypes = ['jpg', 'jpeg', 'png', 'gif']
  return imgTypes.includes(type.toLowerCase())
}

/** 需要处理的文件url服务器地址 */
export const needProcessUrlDomain = [
  // {
  //   domainStr: '协议://ip:端口',
  //   replaceStr: '域名',
  // },
]

/** 预览附件 */
export const previewAnnex = (annex: DisplayFile) => {
  let url = annex.url
  const type = annex.fileType ? annex.fileType : getFileType(annex.url)

  // 如果url需要处理的话
  needProcessUrlDomain.forEach((item) => {
    if (url.includes(item.domainStr)) {
      url = url.replace(item.domainStr, item.replaceStr)
    }
  })

  // 如果是小程序走这里
  // #ifdef MP-WEIXIN
  if (isImage(type)) {
    uni.previewImage({
      current: 0,
      urls: [url],
    })
  } else {
    previewDocument(url, type)
  }
  // #endif

  // 如果是app走这里
  // #ifdef APP-PLUS
  uni.downloadFile({
    url,
    success: function (res) {
      const filePath = res.tempFilePath
      if (type === 'jpg' || type === 'jpeg' || type === 'png' || type === 'gif') {
        // 如果是图片走这里
        uni.previewImage({
          current: 0,
          urls: [url],
        })
      } else {
        // 如果是文件走这里
        uni.openDocument({
          filePath,
          success: (res) => console.log('成功打开文档'),
          fail: (res) => {
            uni.showToast({
              icon: 'none',
              title: '文件打开失败',
              duration: 2000,
            })
          },
        })
      }
    },
  })
  // #endif
  // #ifdef H5
  // 如果是浏览器走这里
  window.open(url, '_blank')
  // #endif
}

/**
 * 预览 文档 文件
 * @param {string}  - 文件路径
 * @param {string}  - 文件类型
 */
export function previewDocument(url: string, type: string = 'pdf') {
  if (!url) {
    uni.showToast({ title: '无效的文件路径', icon: 'none' })
    return
  }
  // 针对不同平台使用不同方法打开 PDF
  if (PLATFORM.isH5) {
    console.log('h5 新开页面预览')
    window.open(url, '_blank') // H5 打开新窗口
  } else if (PLATFORM.isMp) {
    console.log('文档预览')

    uni.showLoading({
      mask: true,
    })
    uni.downloadFile({
      url, // 远程文件 URL
      success: (res) => {
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: type,
            success: () => {
              console.log('打开文档成功')
            },
            fail: (err) => {
              console.error('打开文档失败，尝试保存文档到本地', err)
              uni.saveFile({
                tempFilePath: res.tempFilePath,
                success: (saveRes) => {
                  console.log('文件已保存', saveRes.savedFilePath)
                },
                fail: (err) => {
                  uni.showToast({
                    icon: 'none',
                    title: '文件保存失败',
                    duration: 2000,
                  })
                  console.error('保存文件失败', err)
                },
              })
            },
          })
        }
      },
      fail: (err) => {
        console.error('下载文件失败', err)
        uni.showToast({
          icon: 'none',
          title: '文件下载失败:' + (err.errMsg || ''),
          duration: 2000,
        })
      },
      complete: () => {
        uni.hideLoading()
      },
    })
  }
}
