import { ElImageViewer, ElMessage } from "element-plus";
import { createVNode, render } from "vue";
import { FileAPI } from "@/api/file";

/**
 * 文件预览Hook
 * 支持图片预览和文件下载功能
 * @param config 配置选项
 * @returns 文件预览相关方法
 */
export function useFilePreview(config?: FilePreviewConfig): UseFilePreviewReturn {
  // 默认配置
  const defaultConfig: FilePreviewConfig = {
    imageExtensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"],
    baseApiUrl: import.meta.env.VITE_APP_BASE_API || "",
    zIndex: 9999,
    hideOnClickModal: true,
  };

  // 合并配置
  const finalConfig = { ...defaultConfig, ...config };
  /**
   * 判断是否为图片文件
   * @param fileName 文件名或文件路径
   * @returns 是否为图片
   */
  const isImageFile = (fileName: string): boolean => {
    if (!fileName) return false;

    // 获取文件扩展名
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension ? (finalConfig.imageExtensions || []).includes(extension) : false;
  };

  /**
   * 获取完整的文件URL
   * @param url 文件路径
   * @returns 完整的文件URL
   */
  const getFullUrl = (url: string): string => {
    if (!url) return "";

    // 如果已经是完整的URL，直接返回
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }

    // 如果是相对路径，拼接基础API地址
    return finalConfig.baseApiUrl + url;
  };

  /**
   * 预览图片
   * @param imageUrl 图片URL
   * @param imageList 图片列表（用于支持多图预览）
   * @param initialIndex 初始显示的图片索引
   */
  const previewImage = (imageUrl: string, imageList?: string[], initialIndex: number = 0): void => {
    try {
      // 处理图片URL列表
      const urlList =
        imageList && imageList.length > 0
          ? imageList.map((url) => getFullUrl(url))
          : [getFullUrl(imageUrl)];

      // 创建图片预览组件
      const imageViewerVNode = createVNode(ElImageViewer, {
        urlList,
        initialIndex,
        onClose: () => {
          // 清理DOM节点
          if (container && document.body.contains(container)) {
            document.body.removeChild(container);
          }
        },
        zIndex: finalConfig.zIndex,
        hideOnClickModal: finalConfig.hideOnClickModal,
      });

      // 创建容器并渲染
      const container = document.createElement("div");
      document.body.appendChild(container);
      render(imageViewerVNode, container);
    } catch (error) {
      console.error("图片预览失败:", error);
      ElMessage.error("图片预览失败");
    }
  };

  /**
   * 下载文件
   * @param fileUrl 文件URL
   * @param fileName 自定义文件名（可选）
   */
  const downloadFile = async (fileUrl: string, fileName?: string): Promise<void> => {
    try {
      if (!fileUrl) {
        ElMessage.error("文件地址不能为空");
        return;
      }

      const fullUrl = getFullUrl(fileUrl);

      // 如果没有提供文件名，从URL中提取
      let downloadFileName = fileName;
      if (!downloadFileName) {
        const urlParts = fullUrl.split("/");
        downloadFileName = urlParts[urlParts.length - 1] || "下载文件";

        // 如果从URL中提取的文件名没有扩展名，尝试从原始URL中获取
        if (!downloadFileName.includes(".")) {
          const originalParts = fileUrl.split("/");
          const originalFileName = originalParts[originalParts.length - 1];
          if (originalFileName && originalFileName.includes(".")) {
            downloadFileName = originalFileName;
          }
        }
      }

      // 使用FileAPI下载文件
      await FileAPI.download(fullUrl, downloadFileName);
    } catch (error) {
      console.error("文件下载失败:", error);
      ElMessage.error("文件下载失败");
    }
  };

  /**
   * 预览附件（主要方法）
   * @param fileUrl 文件URL
   * @param fileName 文件名（可选，用于下载时的文件名）
   * @param imageList 图片列表（当预览图片时，支持多图浏览）
   * @param initialIndex 初始图片索引（当有图片列表时）
   */
  const previewFile = (
    fileUrl: string,
    fileName?: string,
    imageList?: string[],
    initialIndex: number = 0
  ): void => {
    if (!fileUrl) {
      ElMessage.error("文件地址不能为空");
      return;
    }

    // 判断文件类型
    const fileNameToCheck = fileName || fileUrl;

    if (isImageFile(fileNameToCheck)) {
      // 如果是图片，使用图片预览
      previewImage(fileUrl, imageList, initialIndex);
    } else {
      // 如果是其他文件，直接下载
      downloadFile(fileUrl, fileName);
    }
  };

  /**
   * 批量预览图片（用于图片列表场景）
   * @param imageUrls 图片URL列表
   * @param initialIndex 初始显示的图片索引
   */
  const previewImages = (imageUrls: string[], initialIndex: number = 0): void => {
    if (!imageUrls || imageUrls.length === 0) {
      ElMessage.error("图片列表不能为空");
      return;
    }

    previewImage(imageUrls[initialIndex], imageUrls, initialIndex);
  };

  return {
    isImageFile,
    getFullUrl,
    previewImage,
    downloadFile,
    previewFile,
    previewImages,
  };
}

export default useFilePreview;

/**
 * 文件预览Hook相关类型定义
 */

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件ID */
  id?: string | number;
  /** 文件名 */
  name: string;
  /** 文件URL */
  url: string;
  /** 文件大小（字节） */
  size?: number;
  /** 文件类型 */
  type?: string;
  /** 上传时间 */
  uploadTime?: string;
}

/**
 * 图片信息接口
 */
export interface ImageInfo extends FileInfo {
  /** 图片宽度 */
  width?: number;
  /** 图片高度 */
  height?: number;
  /** 缩略图URL */
  thumbnailUrl?: string;
}

/**
 * 文件预览配置接口
 */
export interface FilePreviewConfig {
  /** 支持预览的图片格式 */
  imageExtensions?: string[];
  /** 基础API地址 */
  baseApiUrl?: string;
  /** 图片预览器的z-index */
  zIndex?: number;
  /** 是否点击遮罩关闭预览 */
  hideOnClickModal?: boolean;
}

/**
 * 下载配置接口
 */
export interface DownloadConfig {
  /** 自定义文件名 */
  fileName?: string;
  /** 下载前的确认提示 */
  confirmMessage?: string;
  /** 下载成功的提示信息 */
  successMessage?: string;
  /** 下载失败的提示信息 */
  errorMessage?: string;
}

/**
 * 文件预览Hook返回值接口
 */
export interface UseFilePreviewReturn {
  /** 判断是否为图片文件 */
  isImageFile: (fileName: string) => boolean;
  /** 获取完整的文件URL */
  getFullUrl: (url: string) => string;
  /** 预览图片 */
  previewImage: (imageUrl: string, imageList?: string[], initialIndex?: number) => void;
  /** 下载文件 */
  downloadFile: (fileUrl: string, fileName?: string) => Promise<void>;
  /** 预览文件（主要方法） */
  previewFile: (
    fileUrl: string,
    fileName?: string,
    imageList?: string[],
    initialIndex?: number
  ) => void;
  /** 批量预览图片 */
  previewImages: (imageUrls: string[], initialIndex?: number) => void;
}

/**
 * 附件预览组件Props接口
 */
export interface AttachmentPreviewProps {
  /** 文件列表 */
  fileList: FileInfo[];
  /** 是否显示文件名 */
  showFileName?: boolean;
  /** 是否显示文件大小 */
  showFileSize?: boolean;
  /** 是否显示下载按钮 */
  showDownloadButton?: boolean;
  /** 图片预览模式：'modal' | 'inline' */
  imagePreviewMode?: "modal" | "inline";
  /** 最大显示文件数量 */
  maxDisplayCount?: number;
}

/**
 * 文件操作事件接口
 */
export interface FileOperationEvents {
  /** 文件预览事件 */
  onPreview?: (file: FileInfo) => void;
  /** 文件下载事件 */
  onDownload?: (file: FileInfo) => void;
  /** 文件删除事件 */
  onDelete?: (file: FileInfo) => void;
  /** 预览错误事件 */
  onPreviewError?: (error: Error, file: FileInfo) => void;
  /** 下载错误事件 */
  onDownloadError?: (error: Error, file: FileInfo) => void;
}
