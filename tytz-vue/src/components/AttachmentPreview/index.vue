<template>
  <div class="attachment-preview">
    <!-- 文件列表 -->
    <div v-if="fileList && fileList.length > 0" class="file-list">
      <div 
        v-for="(file, index) in displayFiles" 
        :key="file.id || index"
        class="file-item"
        @click="handleFileClick(file, index)"
      >
        <!-- 文件图标 -->
        <div class="file-icon">
          <el-icon v-if="isImageFile(file.name)" size="20" color="#67C23A">
            <Picture />
          </el-icon>
          <el-icon v-else size="20" color="#409EFF">
            <Document />
          </el-icon>
        </div>
        
        <!-- 文件信息 -->
        <div class="file-info">
          <div v-if="showFileName" class="file-name" :title="file.name">
            {{ file.name }}
          </div>
          <div v-if="showFileSize && file.size" class="file-size">
            {{ formatFileSize(file.size) }}
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="file-actions">
          <el-button 
            v-if="showDownloadButton" 
            type="text" 
            size="small"
            @click.stop="handleDownload(file)"
          >
            <el-icon><Download /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 更多文件提示 -->
      <div v-if="hasMoreFiles" class="more-files">
        还有 {{ fileList.length - maxDisplayCount }} 个文件...
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-icon size="48" color="#C0C4CC"><Document /></el-icon>
      <p>暂无附件</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Picture, Document, Download } from '@element-plus/icons-vue'
import { useFilePreview } from '@/hooks/useFilePreview'
import type { AttachmentPreviewProps, FileInfo } from '@/hooks/useFilePreview.types'

/**
 * 组件Props定义
 */
const props = withDefaults(defineProps<AttachmentPreviewProps>(), {
  showFileName: true,
  showFileSize: false,
  showDownloadButton: true,
  imagePreviewMode: 'modal',
  maxDisplayCount: 10
})

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  preview: [file: FileInfo]
  download: [file: FileInfo]
  previewError: [error: Error, file: FileInfo]
  downloadError: [error: Error, file: FileInfo]
}>()

// 使用文件预览Hook
const { isImageFile, previewFile, downloadFile, previewImages } = useFilePreview()

/**
 * 显示的文件列表（限制数量）
 */
const displayFiles = computed(() => {
  return props.fileList.slice(0, props.maxDisplayCount)
})

/**
 * 是否有更多文件
 */
const hasMoreFiles = computed(() => {
  return props.fileList.length > props.maxDisplayCount
})

/**
 * 处理文件点击事件
 * @param file 文件对象
 * @param index 文件索引
 */
const handleFileClick = (file: FileInfo, index: number) => {
  try {
    emit('preview', file)
    
    if (isImageFile(file.name)) {
      // 如果是图片，支持图片列表预览
      const imageFiles = props.fileList.filter(f => isImageFile(f.name))
      const imageUrls = imageFiles.map(f => f.url)
      const imageIndex = imageFiles.findIndex(f => f.id === file.id)
      
      if (imageUrls.length > 1) {
        previewImages(imageUrls, imageIndex >= 0 ? imageIndex : 0)
      } else {
        previewFile(file.url, file.name)
      }
    } else {
      // 如果是其他文件，直接预览/下载
      previewFile(file.url, file.name)
    }
  } catch (error) {
    const err = error as Error
    emit('previewError', err, file)
  }
}

/**
 * 处理文件下载
 * @param file 文件对象
 */
const handleDownload = async (file: FileInfo) => {
  try {
    emit('download', file)
    await downloadFile(file.url, file.name)
  } catch (error) {
    const err = error as Error
    emit('downloadError', err, file)
  }
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小
 */
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} B`
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }
}
</script>

<style scoped>
.attachment-preview {
  width: 100%;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.file-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.file-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.file-actions {
  margin-left: 8px;
}

.more-files {
  text-align: center;
  padding: 8px;
  color: #909399;
  font-size: 12px;
  border: 1px dashed #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
